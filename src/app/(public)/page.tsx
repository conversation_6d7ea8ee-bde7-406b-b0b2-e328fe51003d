"use client";

import React from 'react';
import <PERSON> from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Shield,
  Vault,
  Clock,
  FileText,
  Power,
  CheckCircle,
  XCircle
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import MainHeader from '@/components/Navigation/MainHeader';

const features = [
  {
    icon: Shield,
    title: 'Asset Management',
    description: 'Create a comprehensive inventory of both physical and digital assets to ensure nothing is overlooked.'
  },
  {
    icon: Vault,
    title: 'Digital Vault',
    description: 'Securely store sensitive documents that trustees will need access to after your death.'
  },
  {
    icon: Clock,
    title: 'Time Capsule',
    description: 'Schedule messages, videos, or other content to be delivered to loved ones at specific times.'
  },
  {
    icon: FileText,
    title: 'Will Writing Assistance',
    description: 'Document your final wishes in a structured format with our step-by-step guide.'
  },
  {
    icon: Power,
    title: 'Service Sunset',
    description: 'Create a list of services and accounts that should be canceled or transferred after death.'
  }
];

// Updated pricing plans to exactly match subscription plans from SubscriptionPlans.tsx
const pricingPlans = [
  {
    name: 'Essential Legacy',
    price: '$0',
    period: 'forever',
    description: 'Basic digital legacy management for individuals',
    features: [
      { name: 'Unlimited assets', included: true },
      { name: '1 document storage', included: true },
      { name: 'Time capsule messages', included: false },
      { name: 'Basic will advice', included: true },
      { name: 'Up to 1 trustee', included: true },
      { name: 'Premium encryption', included: false },
      { name: 'Priority support', included: false },
      { name: 'Unlimited contacts & wishes', included: true }
    ],
    buttonText: 'Sign Up Free',
    buttonVariant: 'outline' as const
  },
  {
    name: 'Legacy Preserver',
    price: '$29.99',
    period: 'per year',
    description: 'Comprehensive legacy planning for families',
    features: [
      { name: 'Unlimited assets', included: true },
      { name: '5GB vault storage', included: true },
      { name: '100 time capsule messages (up to 10GB)', included: true },
      { name: 'Unlimited will advice', included: true },
      { name: 'Up to 5 trustees', included: true },
      { name: 'Premium encryption', included: true },
      { name: 'Priority support', included: true },
      { name: 'Unlimited contacts & wishes', included: true }
    ],
    buttonText: 'Start Premium',
    buttonVariant: 'default' as const,
    highlight: true
  }
];

export default function LandingPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <MainHeader />

      {/* Hero Section */}
      <header className="bg-gradient-to-r from-gray-900 to-blue-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32">
          <div className="max-w-3xl">
            <h1 className="text-4xl font-extrabold tracking-tight sm:text-5xl md:text-6xl">
              Secure Your Digital Legacy
            </h1>
            <p className="mt-6 text-xl text-gray-300 max-w-2xl">
              Legalock helps you organize, protect, and pass on your digital assets and final wishes to your loved ones.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4">
              <Button size="lg" asChild>
                <Link href="/register">Get Started</Link>
              </Button>
              <Button size="lg" variant="outline" className="bg-opacity-20 backdrop-blur-sm" asChild>
                <Link href="/login">Sign In</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              Your Complete Digital Legacy Solution
            </h2>
            <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
              Legalock provides all the tools you need to ensure your digital assets, documents, and final messages are handled according to your wishes.
            </p>
          </div>

          <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6 flex flex-col items-start">
                <div className="p-2 bg-blue-100 rounded-md mb-4">
                  <feature.icon className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900">{feature.title}</h3>
                <p className="mt-2 text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16" id="pricing">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              Simple, Transparent Pricing
            </h2>
            <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
              Choose the plan that fits your needs. All plans include core legacy planning features.
            </p>
          </div>

          <div className="mt-16 grid gap-8 lg:grid-cols-2 max-w-4xl mx-auto">
            {pricingPlans.map((plan, index) => (
              <Card
                key={index}
                className={`flex flex-col transition-all duration-300 hover:shadow-xl ${
                  plan.highlight ? 'border-primary shadow-lg relative' : ''
                } ${
                  plan.name !== 'Free' ? 'hover:scale-105 hover:z-10' : ''
                }`}
              >
                {plan.highlight && (
                  <div className="absolute top-0 left-0 right-0 transform -translate-y-1/2 flex justify-center">
                    <span className="bg-primary text-white text-sm px-4 py-1 rounded-full font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{plan.name}</CardTitle>
                  <div className="flex items-baseline mt-2">
                    <span className="text-3xl font-extrabold">{plan.price}</span>
                    <span className="ml-1 text-gray-500">/{plan.period}</span>
                  </div>
                  <CardDescription className="mt-2">{plan.description}</CardDescription>
                </CardHeader>
                <CardContent className="flex-grow">
                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        {feature.included ? (
                          <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mr-2" />
                        ) : (
                          <XCircle className="h-5 w-5 text-gray-400 flex-shrink-0 mr-2" />
                        )}
                        <span className={feature.included ? 'text-gray-900' : 'text-gray-500'}>
                          {feature.name}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    variant={plan.buttonVariant}
                    className="w-full"
                    asChild
                  >
                    <Link href="/register">{plan.buttonText}</Link>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials and other sections */}

      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              Trusted by Families Everywhere
            </h2>
          </div>
          <div className="mt-16 grid gap-8 md:grid-cols-3">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <p className="text-gray-600 italic">"Legalock has given me peace of mind knowing my digital assets will be properly managed. The interface is intuitive and the customer support excellent."</p>
              <div className="mt-4">
                <p className="font-medium">Sarah Johnson</p>
                <p className="text-sm text-gray-500">Estate Planner</p>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <p className="text-gray-600 italic">"The time capsule feature is incredible. I've set up messages for my children's future milestones, knowing they'll have my guidance even years from now."</p>
              <div className="mt-4">
                <p className="font-medium">Michael Rodriguez</p>
                <p className="text-sm text-gray-500">Family of Four</p>
              </div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <p className="text-gray-600 italic">"After losing my father without any digital account information, I decided to use Legalock. Now my family won't face the same challenges I did."</p>
              <div className="mt-4">
                <p className="font-medium">Jennifer Lee</p>
                <p className="text-sm text-gray-500">Business Owner</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="bg-blue-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-extrabold sm:text-4xl">
            Ready to secure your digital legacy?
          </h2>
          <p className="mt-4 text-lg text-blue-100 max-w-2xl mx-auto">
            Join thousands of others who have already taken the first step in protecting their digital assets and final wishes.
          </p>
          <div className="mt-8">
            <Button size="lg" className="bg-white text-blue-900 hover:bg-gray-100" asChild>
              <Link href="/register">Get Started Today</Link>
            </Button>
          </div>
        </div>
      </section>

      <footer className="bg-gray-900 text-gray-400 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid gap-8 md:grid-cols-4">
            <div>
              <h3 className="text-white font-bold text-lg mb-4">Legalock</h3>
              <p className="text-sm">Secure your digital legacy for the ones you love.</p>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">Features</h4>
              <ul className="space-y-2 text-sm">
                <li><Link href="#" className="hover:text-white">Asset Management</Link></li>
                <li><Link href="#" className="hover:text-white">Digital Vault</Link></li>
                <li><Link href="#" className="hover:text-white">Time Capsule</Link></li>
                <li><Link href="#" className="hover:text-white">Will Writing</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">Resources</h4>
              <ul className="space-y-2 text-sm">
                <li><Link href="/blog" className="hover:text-white">Blog</Link></li>
                <li><Link href="/help" className="hover:text-white">Help Center</Link></li>
                <li><Link href="/contact" className="hover:text-white">Contact Support</Link></li>
                <li><Link href="/terms" className="hover:text-white">Terms of Service</Link></li>
                <li><Link href="/privacy" className="hover:text-white">Privacy Policy</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">Company</h4>
              <ul className="space-y-2 text-sm">
                <li><Link href="/about" className="hover:text-white">About Us</Link></li>
                <li><Link href="/careers" className="hover:text-white">Careers</Link></li>
                <li><Link href="/security" className="hover:text-white">Security</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-12 pt-8 text-sm text-center">
            <p>&copy; {new Date().getFullYear()} Legalock. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
