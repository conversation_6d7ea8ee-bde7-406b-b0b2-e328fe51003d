import { supabaseAdmin } from '@/lib/supabase-server';
import { User } from '@supabase/supabase-js';

/**
 * Ensures that a mapping exists between custom_users and auth.users
 * If no mapping exists, it will create one
 *
 * @param customUserId The ID of the user in the custom_users table
 * @returns The auth user ID from the mapping
 */
export async function ensureAuthUserMapping(customUserId: string): Promise<string | null> {
  try {
    // First, try to get the existing mapping
    const { data: existingMapping, error: mappingError } = await supabaseAdmin
      .from('auth_users_mapping')
      .select('auth_user_id')
      .eq('custom_user_id', customUserId)
      .maybeSingle();

    // If we already have a mapping, return the auth user ID
    if (existingMapping && existingMapping.auth_user_id) {
      return existingMapping.auth_user_id;
    }

    // If there was an error other than "no rows returned", log it
    if (mappingError && mappingError.code !== 'PGRST116') {
      console.error('Error checking auth user mapping:', mappingError);
      return null;
    }

    // No mapping exists, so we need to create one
    // First, get the user's email and name from custom_users
    const { data: userData, error: userError } = await supabaseAdmin
      .from('custom_users')
      .select('email, first_name, last_name')
      .eq('id', customUserId)
      .single();

    if (userError || !userData) {
      console.error('Error getting user data:', userError);
      return null;
    }

    // Now find the corresponding auth.users record by email
    // Note: auth.users is a special table in Supabase that requires a different query approach
    const { data: authUsers, error: authUserError } = await supabaseAdmin.auth.admin.listUsers();

    // Define the type for users
    type AuthUser = User;

    if (authUserError) {
      console.error('Error listing auth users:', authUserError);
      return null;
    }

    // Find the user with matching email
    let authUser: AuthUser | undefined = authUsers.users.find((user: AuthUser) => {
      return user.email === userData.email;
    });

    // No need to check for PGRST116 error here since we're using the admin API

    // If no auth user exists with this email, we need to create one
    if (!authUser || !authUser.id) {
      console.log('No auth user found with email:', userData.email, 'Creating one...');

      try {
        // Create a new auth user with the same email
        const { data: newAuthUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
          email: userData.email,
          email_confirm: true,
          user_metadata: {
            first_name: userData.first_name || '',
            last_name: userData.last_name || ''
          }
        });

        if (createError) {
          console.error('Error creating auth user:', createError);
          return null;
        }

        if (!newAuthUser || !newAuthUser.user || !newAuthUser.user.id) {
          console.error('Failed to create auth user');
          return null;
        }

        // Use the newly created auth user
        authUser = newAuthUser.user;
      } catch (error) {
        console.error('Exception creating auth user:', error);
        return null;
      }
    }

    // Check if a mapping already exists for this auth user to prevent duplicates
    const { data: existingAuthMapping } = await supabaseAdmin
      .from('auth_users_mapping')
      .select('custom_user_id')
      .eq('auth_user_id', authUser.id)
      .maybeSingle();

    if (existingAuthMapping) {
      console.log('Mapping already exists for auth user:', authUser.id);
      return authUser.id;
    }

    // Create the mapping
    try {
      const { data: newMapping, error: createError } = await supabaseAdmin
        .from('auth_users_mapping')
        .insert({
          custom_user_id: customUserId,
          auth_user_id: authUser.id
        })
        .select('auth_user_id')
        .single();

      if (createError) {
        console.error('Error creating auth user mapping:', createError);
        return null;
      }

      console.log('Successfully created auth user mapping for user:', customUserId, 'with auth user:', authUser.id);
      return newMapping.auth_user_id;
    } catch (error) {
      console.error('Exception creating auth user mapping:', error);
      // If there's an error, but we have the auth user ID, return it anyway as a fallback
      return authUser.id;
    }
  } catch (error) {
    console.error('Exception in ensureAuthUserMapping:', error);
    return null;
  }
}
